# MYMacApi - Node.js + Koa MVC 项目

基于 Node.js + Koa 框架构建的 MVC 架构 API 项目，集成 MySQL 数据库。

## 项目结构

```
MYMacApi/
├── app.js                 # 主应用文件
├── package.json           # 项目配置和依赖
├── .env                   # 环境变量配置
├── config/
│   └── database.js        # 数据库配置
├── controllers/
│   ├── BaseController.js  # 基础控制器
│   └── UserController.js  # 用户控制器
├── models/
│   ├── BaseModel.js       # 基础模型
│   └── User.js            # 用户模型
├── routes/
│   ├── index.js           # 主路由
│   └── users.js           # 用户路由
├── middleware/
│   └── errorHandler.js    # 错误处理中间件
└── utils/                 # 工具函数
```

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置数据库
编辑 `.env` 文件，配置你的 MySQL 数据库连接信息：
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=mymacapi
```

### 3. 启动服务
```bash
# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

服务器将在 http://localhost:3000 启动

## API 接口文档

### 基础接口

#### 健康检查
- **GET** `/health`
- **描述**: 检查服务器运行状态

#### API 信息
- **GET** `/api`
- **描述**: 获取 API 基本信息和可用端点

### 用户管理接口

#### 1. 获取用户列表
- **GET** `/api/users`
- **查询参数**:
  - `page`: 页码（默认: 1）
  - `pageSize`: 每页数量（默认: 10，最大: 100）

#### 2. 获取指定用户
- **GET** `/api/users/:id`
- **路径参数**:
  - `id`: 用户ID

#### 3. 创建用户
- **POST** `/api/users`
- **请求体**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "123456"
}
```

#### 4. 更新用户
- **PUT** `/api/users/:id`
- **路径参数**:
  - `id`: 用户ID
- **请求体**:
```json
{
  "username": "newusername",
  "email": "<EMAIL>",
  "password": "newpassword"
}
```

#### 5. 删除用户
- **DELETE** `/api/users/:id`
- **路径参数**:
  - `id`: 用户ID

## 响应格式

### 成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2023-12-07T10:30:00.000Z"
}
```

### 错误响应
```json
{
  "success": false,
  "code": 400,
  "message": "错误信息",
  "data": null,
  "timestamp": "2023-12-07T10:30:00.000Z"
}
```

## 技术栈

- **Node.js**: JavaScript 运行环境
- **Koa**: Web 框架
- **MySQL**: 关系型数据库
- **mysql2**: MySQL 驱动
- **dotenv**: 环境变量管理

## 开发工具

- **nodemon**: 开发时自动重启
- **jest**: 单元测试框架

## 许可证

MIT
