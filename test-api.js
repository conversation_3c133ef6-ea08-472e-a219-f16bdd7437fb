// API 测试脚本
// 使用方法: node test-api.js

const http = require('http');

const BASE_URL = 'http://localhost:3000';

// 发送HTTP请求的辅助函数
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            data: jsonBody
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: body
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// 测试函数
async function runTests() {
  console.log('🧪 开始API测试...\n');

  try {
    // 1. 测试健康检查
    console.log('1. 测试健康检查接口');
    const healthCheck = await makeRequest('GET', '/health');
    console.log(`   状态码: ${healthCheck.status}`);
    console.log(`   响应: ${JSON.stringify(healthCheck.data, null, 2)}\n`);

    // 2. 测试API信息
    console.log('2. 测试API信息接口');
    const apiInfo = await makeRequest('GET', '/api');
    console.log(`   状态码: ${apiInfo.status}`);
    console.log(`   响应: ${JSON.stringify(apiInfo.data, null, 2)}\n`);

    // 3. 创建测试用户
    console.log('3. 创建测试用户');
    const newUser = {
      username: 'testuser',
      email: '<EMAIL>',
      password: '123456'
    };
    const createResult = await makeRequest('POST', '/api/users', newUser);
    console.log(`   状态码: ${createResult.status}`);
    console.log(`   响应: ${JSON.stringify(createResult.data, null, 2)}\n`);

    let userId = null;
    if (createResult.data.success && createResult.data.data) {
      userId = createResult.data.data.id;
    }

    // 4. 获取用户列表
    console.log('4. 获取用户列表');
    const usersList = await makeRequest('GET', '/api/users?page=1&pageSize=5');
    console.log(`   状态码: ${usersList.status}`);
    console.log(`   响应: ${JSON.stringify(usersList.data, null, 2)}\n`);

    if (userId) {
      // 5. 获取指定用户
      console.log('5. 获取指定用户');
      const userDetail = await makeRequest('GET', `/api/users/${userId}`);
      console.log(`   状态码: ${userDetail.status}`);
      console.log(`   响应: ${JSON.stringify(userDetail.data, null, 2)}\n`);

      // 6. 更新用户
      console.log('6. 更新用户信息');
      const updateData = {
        username: 'updateduser',
        email: '<EMAIL>'
      };
      const updateResult = await makeRequest('PUT', `/api/users/${userId}`, updateData);
      console.log(`   状态码: ${updateResult.status}`);
      console.log(`   响应: ${JSON.stringify(updateResult.data, null, 2)}\n`);

      // 7. 删除用户
      console.log('7. 删除用户');
      const deleteResult = await makeRequest('DELETE', `/api/users/${userId}`);
      console.log(`   状态码: ${deleteResult.status}`);
      console.log(`   响应: ${JSON.stringify(deleteResult.data, null, 2)}\n`);
    }

    // 8. 测试错误处理 - 获取不存在的用户
    console.log('8. 测试错误处理 - 获取不存在的用户');
    const notFoundTest = await makeRequest('GET', '/api/users/99999');
    console.log(`   状态码: ${notFoundTest.status}`);
    console.log(`   响应: ${JSON.stringify(notFoundTest.data, null, 2)}\n`);

    // 9. 测试参数验证 - 创建用户时缺少必需字段
    console.log('9. 测试参数验证 - 创建用户时缺少必需字段');
    const invalidUser = {
      username: 'testuser2'
      // 缺少 email 和 password
    };
    const validationTest = await makeRequest('POST', '/api/users', invalidUser);
    console.log(`   状态码: ${validationTest.status}`);
    console.log(`   响应: ${JSON.stringify(validationTest.data, null, 2)}\n`);

    console.log('✅ API测试完成!');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 检查服务器是否运行
async function checkServer() {
  try {
    await makeRequest('GET', '/health');
    return true;
  } catch (error) {
    return false;
  }
}

// 主函数
async function main() {
  console.log('🔍 检查服务器状态...');
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ 服务器未运行，请先启动服务器:');
    console.log('   npm start 或 npm run dev');
    return;
  }

  console.log('✅ 服务器运行正常，开始测试\n');
  await runTests();
}

main();
