const { pool, isDemoMode } = require('../config/database');

// 内存数据存储（演示模式）
const memoryData = {};

class BaseModel {
  constructor(tableName) {
    this.tableName = tableName;
    this.pool = pool;

    // 初始化内存数据表
    if (isDemoMode && !memoryData[tableName]) {
      memoryData[tableName] = [];
    }
  }

  // 演示模式：内存数据操作方法
  _filterMemoryData(data, conditions) {
    if (Object.keys(conditions).length === 0) return data;
    return data.filter(item => {
      return Object.keys(conditions).every(key => item[key] === conditions[key]);
    });
  }

  _paginateMemoryData(data, limit, offset) {
    if (!limit) return data;
    const start = offset || 0;
    return data.slice(start, start + limit);
  }

  // 查询所有记录
  async findAll(conditions = {}, limit = null, offset = null) {
    if (isDemoMode) {
      const data = memoryData[this.tableName] || [];
      let filtered = this._filterMemoryData(data, conditions);
      return this._paginateMemoryData(filtered, limit, offset);
    }

    try {
      let sql = `SELECT * FROM ${this.tableName}`;
      const params = [];

      // 添加条件
      if (Object.keys(conditions).length > 0) {
        const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
        sql += ` WHERE ${whereClause}`;
        params.push(...Object.values(conditions));
      }

      // 添加分页
      if (limit) {
        sql += ` LIMIT ?`;
        params.push(limit);
        if (offset) {
          sql += ` OFFSET ?`;
          params.push(offset);
        }
      }

      const [rows] = await this.pool.execute(sql, params);
      return rows;
    } catch (error) {
      throw new Error(`查询失败: ${error.message}`);
    }
  }

  // 根据ID查询单条记录
  async findById(id) {
    if (isDemoMode) {
      const data = memoryData[this.tableName] || [];
      return data.find(item => item.id === parseInt(id)) || null;
    }

    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE id = ?`;
      const [rows] = await this.pool.execute(sql, [id]);
      return rows[0] || null;
    } catch (error) {
      throw new Error(`查询失败: ${error.message}`);
    }
  }

  // 创建记录
  async create(data) {
    if (isDemoMode) {
      const table = memoryData[this.tableName];
      const id = table.length > 0 ? Math.max(...table.map(item => item.id)) + 1 : 1;
      const now = new Date().toISOString();
      const newRecord = {
        id,
        ...data,
        created_at: now,
        updated_at: now
      };
      table.push(newRecord);
      return newRecord;
    }

    try {
      const fields = Object.keys(data);
      const values = Object.values(data);
      const placeholders = fields.map(() => '?').join(', ');

      const sql = `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES (${placeholders})`;
      const [result] = await this.pool.execute(sql, values);

      return {
        id: result.insertId,
        ...data
      };
    } catch (error) {
      throw new Error(`创建失败: ${error.message}`);
    }
  }

  // 更新记录
  async update(id, data) {
    if (isDemoMode) {
      const table = memoryData[this.tableName];
      const index = table.findIndex(item => item.id === parseInt(id));
      if (index === -1) {
        throw new Error('记录不存在');
      }
      const now = new Date().toISOString();
      table[index] = {
        ...table[index],
        ...data,
        updated_at: now
      };
      return table[index];
    }

    try {
      const fields = Object.keys(data);
      const values = Object.values(data);
      const setClause = fields.map(field => `${field} = ?`).join(', ');

      const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE id = ?`;
      const [result] = await this.pool.execute(sql, [...values, id]);

      if (result.affectedRows === 0) {
        throw new Error('记录不存在');
      }

      return await this.findById(id);
    } catch (error) {
      throw new Error(`更新失败: ${error.message}`);
    }
  }

  // 删除记录
  async delete(id) {
    if (isDemoMode) {
      const table = memoryData[this.tableName];
      const index = table.findIndex(item => item.id === parseInt(id));
      if (index === -1) {
        throw new Error('记录不存在');
      }
      table.splice(index, 1);
      return { message: '删除成功' };
    }

    try {
      const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;
      const [result] = await this.pool.execute(sql, [id]);

      if (result.affectedRows === 0) {
        throw new Error('记录不存在');
      }

      return { message: '删除成功' };
    } catch (error) {
      throw new Error(`删除失败: ${error.message}`);
    }
  }

  // 统计记录数
  async count(conditions = {}) {
    if (isDemoMode) {
      const data = memoryData[this.tableName] || [];
      const filtered = this._filterMemoryData(data, conditions);
      return filtered.length;
    }

    try {
      let sql = `SELECT COUNT(*) as count FROM ${this.tableName}`;
      const params = [];

      if (Object.keys(conditions).length > 0) {
        const whereClause = Object.keys(conditions).map(key => `${key} = ?`).join(' AND ');
        sql += ` WHERE ${whereClause}`;
        params.push(...Object.values(conditions));
      }

      const [rows] = await this.pool.execute(sql, params);
      return rows[0].count;
    } catch (error) {
      throw new Error(`统计失败: ${error.message}`);
    }
  }
}

module.exports = BaseModel;
module.exports.memoryData = memoryData;
