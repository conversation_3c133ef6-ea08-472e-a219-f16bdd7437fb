const Router = require('koa-router');
const UserController = require('../controllers/UserController');

const router = new Router({
  prefix: '/api/users'
});

const userController = new UserController();

// 获取用户列表
// GET /api/users?page=1&pageSize=10
router.get('/', async (ctx) => {
  await userController.getUsers(ctx);
});

// 根据ID获取用户
// GET /api/users/:id
router.get('/:id', async (ctx) => {
  await userController.getUserById(ctx);
});

// 创建用户
// POST /api/users
router.post('/', async (ctx) => {
  await userController.createUser(ctx);
});

// 更新用户
// PUT /api/users/:id
router.put('/:id', async (ctx) => {
  await userController.updateUser(ctx);
});

// 删除用户
// DELETE /api/users/:id
router.delete('/:id', async (ctx) => {
  await userController.deleteUser(ctx);
});

module.exports = router;
