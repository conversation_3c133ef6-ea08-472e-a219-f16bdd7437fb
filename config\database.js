const mysql = require('mysql2/promise');
require('dotenv').config();

// 检查是否为演示模式
const isDemoMode = process.env.DEMO_MODE === 'true';

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'mymacapi',
  waitForConnections: true,
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 10,
  queueLimit: 0,
  charset: 'utf8mb4'
};

// 创建连接池（仅在非演示模式下）
let pool = null;
if (!isDemoMode) {
  pool = mysql.createPool(dbConfig);
}

// 测试数据库连接
const testConnection = async () => {
  if (isDemoMode) {
    console.log('🎭 演示模式：使用内存数据，跳过数据库连接');
    return true;
  }

  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
};

// 初始化数据库表
const initDatabase = async () => {
  if (isDemoMode) {
    console.log('🎭 演示模式：跳过数据库表初始化');
    return;
  }

  try {
    // 创建用户表
    const createUsersTable = `
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    `;

    await pool.execute(createUsersTable);
    console.log('✅ 数据库表初始化成功');
  } catch (error) {
    console.error('❌ 数据库表初始化失败:', error.message);
  }
};

module.exports = {
  pool,
  testConnection,
  initDatabase,
  isDemoMode
};
