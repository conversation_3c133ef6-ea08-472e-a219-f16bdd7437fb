const BaseModel = require('./BaseModel');
const { isDemoMode } = require('../config/database');

class User extends BaseModel {
  constructor() {
    super('users');

    // 在演示模式下添加一些示例数据
    if (isDemoMode && this._getMemoryData().length === 0) {
      this._initDemoData();
    }
  }

  _getMemoryData() {
    const { memoryData } = require('./BaseModel');
    return memoryData[this.tableName] || [];
  }

  _initDemoData() {
    const demoUsers = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        username: 'testuser',
        email: '<EMAIL>',
        password: 'test123',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    const { memoryData } = require('./BaseModel');
    memoryData[this.tableName] = demoUsers;
  }

  // 根据用户名查找用户
  async findByUsername(username) {
    if (isDemoMode) {
      const data = this._getMemoryData();
      return data.find(user => user.username === username) || null;
    }

    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE username = ?`;
      const [rows] = await this.pool.execute(sql, [username]);
      return rows[0] || null;
    } catch (error) {
      throw new Error(`查询用户失败: ${error.message}`);
    }
  }

  // 根据邮箱查找用户
  async findByEmail(email) {
    if (isDemoMode) {
      const data = this._getMemoryData();
      return data.find(user => user.email === email) || null;
    }

    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE email = ?`;
      const [rows] = await this.pool.execute(sql, [email]);
      return rows[0] || null;
    } catch (error) {
      throw new Error(`查询用户失败: ${error.message}`);
    }
  }

  // 创建用户（重写以添加验证）
  async create(userData) {
    try {
      // 检查用户名是否已存在
      const existingUser = await this.findByUsername(userData.username);
      if (existingUser) {
        throw new Error('用户名已存在');
      }

      // 检查邮箱是否已存在
      const existingEmail = await this.findByEmail(userData.email);
      if (existingEmail) {
        throw new Error('邮箱已存在');
      }

      // 调用父类的create方法
      return await super.create(userData);
    } catch (error) {
      throw new Error(`创建用户失败: ${error.message}`);
    }
  }

  // 更新用户（重写以添加验证）
  async update(id, userData) {
    try {
      // 如果更新用户名，检查是否已存在
      if (userData.username) {
        const existingUser = await this.findByUsername(userData.username);
        if (existingUser && existingUser.id !== parseInt(id)) {
          throw new Error('用户名已存在');
        }
      }

      // 如果更新邮箱，检查是否已存在
      if (userData.email) {
        const existingEmail = await this.findByEmail(userData.email);
        if (existingEmail && existingEmail.id !== parseInt(id)) {
          throw new Error('邮箱已存在');
        }
      }

      // 调用父类的update方法
      return await super.update(id, userData);
    } catch (error) {
      throw new Error(`更新用户失败: ${error.message}`);
    }
  }

  // 获取用户列表（不返回密码）
  async getUserList(page = 1, pageSize = 10) {
    if (isDemoMode) {
      const data = this._getMemoryData();
      const offset = (page - 1) * pageSize;
      const paginatedData = data.slice(offset, offset + pageSize);

      // 移除密码字段
      const safeData = paginatedData.map(user => {
        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });

      return {
        data: safeData,
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: data.length,
          totalPages: Math.ceil(data.length / pageSize)
        }
      };
    }

    try {
      const offset = (page - 1) * pageSize;
      const sql = `SELECT id, username, email, created_at, updated_at FROM ${this.tableName} LIMIT ? OFFSET ?`;
      const [rows] = await this.pool.execute(sql, [pageSize, offset]);

      const total = await this.count();

      return {
        data: rows,
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      };
    } catch (error) {
      throw new Error(`获取用户列表失败: ${error.message}`);
    }
  }

  // 根据ID获取用户（不返回密码）
  async getUserById(id) {
    if (isDemoMode) {
      const data = this._getMemoryData();
      const user = data.find(u => u.id === parseInt(id));
      if (!user) return null;

      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    }

    try {
      const sql = `SELECT id, username, email, created_at, updated_at FROM ${this.tableName} WHERE id = ?`;
      const [rows] = await this.pool.execute(sql, [id]);
      return rows[0] || null;
    } catch (error) {
      throw new Error(`获取用户失败: ${error.message}`);
    }
  }
}

module.exports = User;
