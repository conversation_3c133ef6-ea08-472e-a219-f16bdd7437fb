const Koa = require('koa');
const bodyParser = require('koa-bodyparser');
const cors = require('koa-cors');
const json = require('koa-json');
const logger = require('koa-logger');
require('dotenv').config();

// 导入中间件和路由
const errorHandler = require('./middleware/errorHandler');
const routes = require('./routes');
const { testConnection, initDatabase } = require('./config/database');

// 创建Koa应用实例
const app = new Koa();

// 全局错误处理
app.use(errorHandler);

// 中间件配置
app.use(cors()); // 跨域支持
app.use(logger()); // 请求日志
app.use(json()); // JSON美化输出
app.use(bodyParser()); // 请求体解析

// 注册路由
app.use(routes.routes());
app.use(routes.allowedMethods());

// 404处理
app.use(async (ctx) => {
  ctx.status = 404;
  ctx.body = {
    success: false,
    code: 404,
    message: '请求的资源不存在',
    timestamp: new Date().toISOString()
  };
});

// 应用级错误监听
app.on('error', (err, ctx) => {
  console.error('应用错误:', err);
});

// 启动服务器
const startServer = async () => {
  try {
    // 测试数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ 数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    // 初始化数据库表
    await initDatabase();

    // 启动HTTP服务器
    const PORT = process.env.PORT || 3000;
    app.listen(PORT, () => {
      console.log('🚀 服务器启动成功!');
      console.log(`📍 服务地址: http://localhost:${PORT}`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log('📚 API文档:');
      console.log(`   - 健康检查: GET http://localhost:${PORT}/health`);
      console.log(`   - API根路径: GET http://localhost:${PORT}/api`);
      console.log(`   - 用户管理: http://localhost:${PORT}/api/users`);
      console.log('');
      console.log('🔧 可用的API端点:');
      console.log('   GET    /api/users          - 获取用户列表');
      console.log('   GET    /api/users/:id      - 获取指定用户');
      console.log('   POST   /api/users          - 创建新用户');
      console.log('   PUT    /api/users/:id      - 更新用户信息');
      console.log('   DELETE /api/users/:id      - 删除用户');
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('📴 收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('📴 收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 启动应用
startServer();

module.exports = app;
