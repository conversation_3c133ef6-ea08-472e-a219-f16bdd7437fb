// 手动测试脚本 - 创建新用户
const http = require('http');

const BASE_URL = 'http://localhost:3000';

function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            data: jsonBody
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: body
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testNewUser() {
  console.log('🧪 测试创建新用户...\n');

  try {
    // 创建新用户
    console.log('1. 创建新用户');
    const newUser = {
      username: 'newuser' + Date.now(),
      email: 'newuser' + Date.now() + '@example.com',
      password: '123456'
    };
    const createResult = await makeRequest('POST', '/api/users', newUser);
    console.log(`   状态码: ${createResult.status}`);
    console.log(`   响应: ${JSON.stringify(createResult.data, null, 2)}\n`);

    if (createResult.data.success && createResult.data.data) {
      const userId = createResult.data.data.id;
      
      // 获取创建的用户
      console.log('2. 获取创建的用户');
      const userDetail = await makeRequest('GET', `/api/users/${userId}`);
      console.log(`   状态码: ${userDetail.status}`);
      console.log(`   响应: ${JSON.stringify(userDetail.data, null, 2)}\n`);

      // 更新用户
      console.log('3. 更新用户信息');
      const updateData = {
        username: 'updated' + Date.now(),
        email: 'updated' + Date.now() + '@example.com'
      };
      const updateResult = await makeRequest('PUT', `/api/users/${userId}`, updateData);
      console.log(`   状态码: ${updateResult.status}`);
      console.log(`   响应: ${JSON.stringify(updateResult.data, null, 2)}\n`);

      // 删除用户
      console.log('4. 删除用户');
      const deleteResult = await makeRequest('DELETE', `/api/users/${userId}`);
      console.log(`   状态码: ${deleteResult.status}`);
      console.log(`   响应: ${JSON.stringify(deleteResult.data, null, 2)}\n`);
    }

    console.log('✅ 测试完成!');
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testNewUser();
