// 全局错误处理中间件
const errorHandler = async (ctx, next) => {
  try {
    await next();
  } catch (error) {
    console.error('服务器错误:', error);
    
    // 设置状态码
    ctx.status = error.status || error.statusCode || 500;
    
    // 错误响应格式
    ctx.body = {
      success: false,
      code: ctx.status,
      message: error.message || '服务器内部错误',
      timestamp: new Date().toISOString()
    };

    // 在开发环境下返回错误堆栈
    if (process.env.NODE_ENV === 'development') {
      ctx.body.stack = error.stack;
    }

    // 触发应用级错误事件
    ctx.app.emit('error', error, ctx);
  }
};

module.exports = errorHandler;
