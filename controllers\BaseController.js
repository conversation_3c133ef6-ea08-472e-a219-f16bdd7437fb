class BaseController {
  // 成功响应
  success(ctx, data = null, message = '操作成功', code = 200) {
    ctx.status = code;
    ctx.body = {
      success: true,
      code,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  }

  // 错误响应
  error(ctx, message = '操作失败', code = 500, data = null) {
    ctx.status = code;
    ctx.body = {
      success: false,
      code,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  }

  // 验证失败响应
  validationError(ctx, message = '参数验证失败', errors = null) {
    this.error(ctx, message, 400, errors);
  }

  // 未找到响应
  notFound(ctx, message = '资源未找到') {
    this.error(ctx, message, 404);
  }

  // 未授权响应
  unauthorized(ctx, message = '未授权访问') {
    this.error(ctx, message, 401);
  }

  // 禁止访问响应
  forbidden(ctx, message = '禁止访问') {
    this.error(ctx, message, 403);
  }

  // 获取分页参数
  getPaginationParams(ctx) {
    const { page = 1, pageSize = 10 } = ctx.query;
    return {
      page: Math.max(1, parseInt(page)),
      pageSize: Math.min(100, Math.max(1, parseInt(pageSize)))
    };
  }

  // 验证必需参数
  validateRequired(data, requiredFields) {
    const missing = [];
    for (const field of requiredFields) {
      if (!data[field] && data[field] !== 0) {
        missing.push(field);
      }
    }
    return missing;
  }

  // 过滤对象属性
  filterFields(obj, allowedFields) {
    const filtered = {};
    for (const field of allowedFields) {
      if (obj.hasOwnProperty(field)) {
        filtered[field] = obj[field];
      }
    }
    return filtered;
  }
}

module.exports = BaseController;
