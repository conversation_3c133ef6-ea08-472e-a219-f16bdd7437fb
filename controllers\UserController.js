const BaseController = require('./BaseController');
const User = require('../models/User');

class UserController extends BaseController {
  constructor() {
    super();
    this.userModel = new User();
  }

  // 获取用户列表
  async getUsers(ctx) {
    try {
      const { page, pageSize } = this.getPaginationParams(ctx);
      const result = await this.userModel.getUserList(page, pageSize);
      this.success(ctx, result, '获取用户列表成功');
    } catch (error) {
      console.error('获取用户列表失败:', error);
      this.error(ctx, error.message);
    }
  }

  // 根据ID获取用户
  async getUserById(ctx) {
    try {
      const { id } = ctx.params;
      
      if (!id || isNaN(id)) {
        return this.validationError(ctx, '用户ID无效');
      }

      const user = await this.userModel.getUserById(id);
      if (!user) {
        return this.notFound(ctx, '用户不存在');
      }

      this.success(ctx, user, '获取用户信息成功');
    } catch (error) {
      console.error('获取用户失败:', error);
      this.error(ctx, error.message);
    }
  }

  // 创建用户
  async createUser(ctx) {
    try {
      const { username, email, password } = ctx.request.body;

      // 验证必需字段
      const missing = this.validateRequired(ctx.request.body, ['username', 'email', 'password']);
      if (missing.length > 0) {
        return this.validationError(ctx, `缺少必需字段: ${missing.join(', ')}`);
      }

      // 简单的邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return this.validationError(ctx, '邮箱格式无效');
      }

      // 用户名长度验证
      if (username.length < 3 || username.length > 50) {
        return this.validationError(ctx, '用户名长度必须在3-50个字符之间');
      }

      // 密码长度验证
      if (password.length < 6) {
        return this.validationError(ctx, '密码长度至少6个字符');
      }

      const userData = this.filterFields(ctx.request.body, ['username', 'email', 'password']);
      const user = await this.userModel.create(userData);

      // 返回时不包含密码
      const { password: _, ...userWithoutPassword } = user;
      this.success(ctx, userWithoutPassword, '用户创建成功', 201);
    } catch (error) {
      console.error('创建用户失败:', error);
      this.error(ctx, error.message);
    }
  }

  // 更新用户
  async updateUser(ctx) {
    try {
      const { id } = ctx.params;
      
      if (!id || isNaN(id)) {
        return this.validationError(ctx, '用户ID无效');
      }

      // 检查用户是否存在
      const existingUser = await this.userModel.getUserById(id);
      if (!existingUser) {
        return this.notFound(ctx, '用户不存在');
      }

      const { username, email, password } = ctx.request.body;

      // 验证邮箱格式（如果提供）
      if (email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          return this.validationError(ctx, '邮箱格式无效');
        }
      }

      // 验证用户名长度（如果提供）
      if (username && (username.length < 3 || username.length > 50)) {
        return this.validationError(ctx, '用户名长度必须在3-50个字符之间');
      }

      // 验证密码长度（如果提供）
      if (password && password.length < 6) {
        return this.validationError(ctx, '密码长度至少6个字符');
      }

      const updateData = this.filterFields(ctx.request.body, ['username', 'email', 'password']);
      
      // 如果没有提供任何更新数据
      if (Object.keys(updateData).length === 0) {
        return this.validationError(ctx, '没有提供要更新的数据');
      }

      const updatedUser = await this.userModel.update(id, updateData);
      
      // 返回时不包含密码
      const { password: _, ...userWithoutPassword } = updatedUser;
      this.success(ctx, userWithoutPassword, '用户更新成功');
    } catch (error) {
      console.error('更新用户失败:', error);
      this.error(ctx, error.message);
    }
  }

  // 删除用户
  async deleteUser(ctx) {
    try {
      const { id } = ctx.params;
      
      if (!id || isNaN(id)) {
        return this.validationError(ctx, '用户ID无效');
      }

      // 检查用户是否存在
      const existingUser = await this.userModel.getUserById(id);
      if (!existingUser) {
        return this.notFound(ctx, '用户不存在');
      }

      await this.userModel.delete(id);
      this.success(ctx, null, '用户删除成功');
    } catch (error) {
      console.error('删除用户失败:', error);
      this.error(ctx, error.message);
    }
  }
}

module.exports = UserController;
