const Router = require('koa-router');
const userRoutes = require('./users');

const router = new Router();

// 健康检查接口
router.get('/health', async (ctx) => {
  ctx.body = {
    success: true,
    message: 'API服务运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  };
});

// API根路径
router.get('/api', async (ctx) => {
  ctx.body = {
    success: true,
    message: '欢迎使用 MYMacApi',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    endpoints: {
      users: '/api/users',
      health: '/health'
    }
  };
});

// 注册子路由
router.use(userRoutes.routes(), userRoutes.allowedMethods());

module.exports = router;
